using UnityEngine;
using Game.Data;
using Game.Events;

namespace Game.GUI
{
    /// <summary>
    /// Runtime GUI for configuring GeneratorConfig data using Unity's immediate mode GUI.
    /// </summary>
    public class RuntimeConfigGUI : MonoBehaviour
    {
        #region Fields

        [Header("GUI Settings")]
        [SerializeField] private bool showGUI = true;
        [SerializeField] private Rect windowRect = new Rect(20, 20, 400, 600);
        [SerializeField] private Vector2 scrollPosition = Vector2.zero;

        [Header("Events")]
        [SerializeField] private ConfigSubmittedEvent onConfigSubmitted;

        // GUI input fields
        private string geminiApiKey = "";
        private string geminiModel = "gemini-2.5-flash";
        private string geminiSystemInstruction = "You are a helpful AI assistant.";
        private string geminiTemperature = "1.0";
        private string geminiTopP = "0.95";
        private string geminiTopK = "40";
        private string geminiThinkingBudget = "-1";

        private string openAIApiKey = "";
        private string openAIApiUrl = "https://api.openai.com/v1/audio/speech";
        private string openAIModel = "tts-1";

        private string assetAddresses = "";
        private string scenario = "";

        // GUI style variables
        private GUIStyle windowStyle;
        private GUIStyle labelStyle;
        private GUIStyle textFieldStyle;
        private GUIStyle buttonStyle;
        private bool stylesInitialized = false;

        #endregion

        #region Properties

        /// <summary>
        /// Gets or sets whether the GUI is visible.
        /// </summary>
        public bool ShowGUI
        {
            get => showGUI;
            set => showGUI = value;
        }

        #endregion

        #region Unity Lifecycle

        private void Start()
        {
            // Initialize event if not assigned
            if (onConfigSubmitted == null)
            {
                onConfigSubmitted = ScriptableObject.CreateInstance<ConfigSubmittedEvent>();
            }

            // Load default values
            LoadDefaultValues();
        }

        private void OnGUI()
        {
            if (!showGUI) return;

            InitializeStyles();
            windowRect = GUI.Window(0, windowRect, DrawConfigWindow, "Animation Generator Config", windowStyle);
        }

        #endregion

        #region GUI Drawing

        private void DrawConfigWindow(int windowID)
        {
            GUILayout.BeginVertical();

            scrollPosition = GUILayout.BeginScrollView(scrollPosition, GUILayout.Height(550));

            DrawGeminiSection();
            GUILayout.Space(10);
            DrawOpenAISection();
            GUILayout.Space(10);
            DrawGeneralSection();

            GUILayout.EndScrollView();

            GUILayout.Space(10);
            DrawButtons();

            GUILayout.EndVertical();

            GUI.DragWindow();
        }

        private void DrawGeminiSection()
        {
            GUILayout.Label("Gemini Configuration", labelStyle);

            GUILayout.Label("API Key:");
            geminiApiKey = GUILayout.TextField(geminiApiKey, textFieldStyle);

            GUILayout.Label("Model:");
            geminiModel = GUILayout.TextField(geminiModel, textFieldStyle);

            GUILayout.Label("System Instruction:");
            geminiSystemInstruction = GUILayout.TextArea(geminiSystemInstruction, GUILayout.Height(60));

            GUILayout.Label("Temperature (0.0 - 2.0):");
            geminiTemperature = GUILayout.TextField(geminiTemperature, textFieldStyle);

            GUILayout.Label("Top P (0.0 - 1.0):");
            geminiTopP = GUILayout.TextField(geminiTopP, textFieldStyle);

            GUILayout.Label("Top K:");
            geminiTopK = GUILayout.TextField(geminiTopK, textFieldStyle);

            GUILayout.Label("Thinking Budget (-1 for unlimited):");
            geminiThinkingBudget = GUILayout.TextField(geminiThinkingBudget, textFieldStyle);
        }

        private void DrawOpenAISection()
        {
            GUILayout.Label("OpenAI Text-to-Speech Configuration", labelStyle);

            GUILayout.Label("API Key:");
            openAIApiKey = GUILayout.TextField(openAIApiKey, textFieldStyle);

            GUILayout.Label("API URL:");
            openAIApiUrl = GUILayout.TextField(openAIApiUrl, textFieldStyle);

            GUILayout.Label("Model:");
            openAIModel = GUILayout.TextField(openAIModel, textFieldStyle);
        }

        private void DrawGeneralSection()
        {
            GUILayout.Label("General Configuration", labelStyle);

            GUILayout.Label("Asset Addresses:");
            assetAddresses = GUILayout.TextArea(assetAddresses, GUILayout.Height(60));

            GUILayout.Label("Scenario:");
            scenario = GUILayout.TextArea(scenario, GUILayout.Height(80));
        }

        private void DrawButtons()
        {
            GUILayout.BeginHorizontal();

            if (GUILayout.Button("Reset to Defaults", buttonStyle))
            {
                LoadDefaultValues();
            }

            if (GUILayout.Button("Submit Configuration", buttonStyle))
            {
                SubmitConfiguration();
            }

            GUILayout.EndHorizontal();
        }

        #endregion

        #region Configuration Management

        private void LoadDefaultValues()
        {
            var defaultConfig = new GeneratorConfig
            {
                geminiConfig = new GeminiConfig(),
                openAITextToSpeechConfig = new OpenAITextToSpeechConfig(),
                assetAddresses = "",
                scenario = ""
            };

            SetFieldsFromConfig(defaultConfig);
        }

        private void SetFieldsFromConfig(GeneratorConfig config)
        {
            if (config.geminiConfig != null)
            {
                geminiApiKey = config.geminiConfig.apiKey ?? "";
                geminiModel = config.geminiConfig.model;
                geminiSystemInstruction = config.geminiConfig.systemInstruction;
                geminiTemperature = config.geminiConfig.temperature.ToString("F2");
                geminiTopP = config.geminiConfig.topP.ToString("F2");
                geminiTopK = config.geminiConfig.topK.ToString();
                geminiThinkingBudget = config.geminiConfig.thinkingBudget.ToString();
            }

            if (config.openAITextToSpeechConfig != null)
            {
                openAIApiKey = config.openAITextToSpeechConfig.apiKey ?? "";
                openAIApiUrl = config.openAITextToSpeechConfig.apiUrl;
                openAIModel = config.openAITextToSpeechConfig.model;
            }

            assetAddresses = config.assetAddresses ?? "";
            scenario = config.scenario ?? "";
        }

        private GeneratorConfig CreateConfigFromFields()
        {
            var config = new GeneratorConfig
            {
                geminiConfig = new GeminiConfig
                {
                    apiKey = geminiApiKey,
                    model = geminiModel,
                    systemInstruction = geminiSystemInstruction,
                    temperature = ParseFloat(geminiTemperature, 1.0f),
                    topP = ParseFloat(geminiTopP, 0.95f),
                    topK = ParseInt(geminiTopK, 40),
                    thinkingBudget = ParseInt(geminiThinkingBudget, -1)
                },
                openAITextToSpeechConfig = new OpenAITextToSpeechConfig
                {
                    apiKey = openAIApiKey,
                    apiUrl = openAIApiUrl,
                    model = openAIModel
                },
                assetAddresses = assetAddresses,
                scenario = scenario
            };

            return config;
        }

        private void SubmitConfiguration()
        {
            var config = CreateConfigFromFields();

            if (onConfigSubmitted != null)
            {
                onConfigSubmitted.Raise(config);
                Debug.Log("Configuration submitted successfully!");
            }
            else
            {
                Debug.LogWarning("No event listener assigned for configuration submission.");
            }
        }

        #endregion

        #region Utility Methods

        private float ParseFloat(string value, float defaultValue)
        {
            return float.TryParse(value, out float result) ? result : defaultValue;
        }

        private int ParseInt(string value, int defaultValue)
        {
            return int.TryParse(value, out int result) ? result : defaultValue;
        }

        private void InitializeStyles()
        {
            if (stylesInitialized) return;

            windowStyle = new GUIStyle(GUI.skin.window)
            {
                fontSize = 14,
                fontStyle = FontStyle.Bold
            };

            labelStyle = new GUIStyle(GUI.skin.label)
            {
                fontSize = 12,
                fontStyle = FontStyle.Bold,
                margin = new RectOffset(0, 0, 5, 2)
            };

            textFieldStyle = new GUIStyle(GUI.skin.textField)
            {
                fontSize = 11,
                margin = new RectOffset(0, 0, 2, 5)
            };

            buttonStyle = new GUIStyle(GUI.skin.button)
            {
                fontSize = 12,
                fontStyle = FontStyle.Bold,
                fixedHeight = 30
            };

            stylesInitialized = true;
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Loads configuration data into the GUI fields.
        /// </summary>
        /// <param name="config">The configuration to load.</param>
        public void LoadConfiguration(GeneratorConfig config)
        {
            if (config != null)
            {
                SetFieldsFromConfig(config);
            }
        }

        /// <summary>
        /// Gets the current configuration from the GUI fields.
        /// </summary>
        /// <returns>The current configuration.</returns>
        public GeneratorConfig GetCurrentConfiguration()
        {
            return CreateConfigFromFields();
        }

        /// <summary>
        /// Toggles the GUI visibility.
        /// </summary>
        public void ToggleGUI()
        {
            showGUI = !showGUI;
        }

        /// <summary>
        /// Sets the config submitted event.
        /// </summary>
        /// <param name="eventAsset">The event asset to use.</param>
        public void SetConfigSubmittedEvent(ConfigSubmittedEvent eventAsset)
        {
            onConfigSubmitted = eventAsset;
        }

        #endregion
    }
}
