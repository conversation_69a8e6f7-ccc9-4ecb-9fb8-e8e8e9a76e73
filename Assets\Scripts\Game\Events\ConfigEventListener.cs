using UnityEngine;
using Game.Data;

namespace Game.Events
{
    /// <summary>
    /// Example component that listens to ConfigSubmittedEvent and processes the submitted configuration.
    /// </summary>
    public class ConfigEventListener : MonoBehaviour
    {
        #region Fields

        [Header("Event Configuration")]
        [SerializeField] private ConfigSubmittedEvent configSubmittedEvent;

        #endregion

        #region Unity Lifecycle

        private void OnEnable()
        {
            if (configSubmittedEvent != null)
            {
                configSubmittedEvent.AddListener(OnConfigurationSubmitted);
            }
        }

        private void OnDisable()
        {
            if (configSubmittedEvent != null)
            {
                configSubmittedEvent.RemoveListener(OnConfigurationSubmitted);
            }
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// Handles the configuration submitted event.
        /// </summary>
        /// <param name="config">The submitted configuration.</param>
        private void OnConfigurationSubmitted(GeneratorConfig config)
        {
            Debug.Log("Configuration received by listener!");
            
            if (config == null)
            {
                Debug.LogError("Received null configuration!");
                return;
            }

            LogConfigurationDetails(config);
            ProcessConfiguration(config);
        }

        #endregion

        #region Configuration Processing

        /// <summary>
        /// Logs the details of the submitted configuration.
        /// </summary>
        /// <param name="config">The configuration to log.</param>
        private void LogConfigurationDetails(GeneratorConfig config)
        {
            Debug.Log("=== Configuration Details ===");
            
            if (config.geminiConfig != null)
            {
                Debug.Log($"Gemini Model: {config.geminiConfig.model}");
                Debug.Log($"Gemini Temperature: {config.geminiConfig.temperature}");
                Debug.Log($"Gemini System Instruction: {config.geminiConfig.systemInstruction}");
                Debug.Log($"Gemini API Key Set: {!string.IsNullOrEmpty(config.geminiConfig.apiKey)}");
            }

            if (config.openAITextToSpeechConfig != null)
            {
                Debug.Log($"OpenAI Model: {config.openAITextToSpeechConfig.model}");
                Debug.Log($"OpenAI API URL: {config.openAITextToSpeechConfig.apiUrl}");
                Debug.Log($"OpenAI API Key Set: {!string.IsNullOrEmpty(config.openAITextToSpeechConfig.apiKey)}");
            }

            Debug.Log($"Asset Addresses: {config.assetAddresses}");
            Debug.Log($"Scenario: {config.scenario}");
            Debug.Log("=== End Configuration Details ===");
        }

        /// <summary>
        /// Processes the submitted configuration. Override this method to implement custom logic.
        /// </summary>
        /// <param name="config">The configuration to process.</param>
        protected virtual void ProcessConfiguration(GeneratorConfig config)
        {
            // Example processing - you can override this method or modify it as needed
            
            // Validate configuration
            if (ValidateConfiguration(config))
            {
                Debug.Log("Configuration is valid and ready for use!");
                
                // Here you would typically:
                // 1. Save the configuration to a persistent location
                // 2. Initialize systems with the new configuration
                // 3. Trigger the animation generation process
                // 4. Update UI elements to reflect the new configuration
                
                // Example: Save to PlayerPrefs (you might want to use a more robust solution)
                SaveConfigurationToPlayerPrefs(config);
            }
            else
            {
                Debug.LogError("Configuration validation failed!");
            }
        }

        /// <summary>
        /// Validates the submitted configuration.
        /// </summary>
        /// <param name="config">The configuration to validate.</param>
        /// <returns>True if the configuration is valid, false otherwise.</returns>
        private bool ValidateConfiguration(GeneratorConfig config)
        {
            bool isValid = true;

            // Check Gemini configuration
            if (config.geminiConfig == null)
            {
                Debug.LogError("Gemini configuration is missing!");
                isValid = false;
            }
            else
            {
                if (string.IsNullOrEmpty(config.geminiConfig.apiKey))
                {
                    Debug.LogWarning("Gemini API key is empty!");
                }

                if (config.geminiConfig.temperature < 0f || config.geminiConfig.temperature > 2f)
                {
                    Debug.LogError("Gemini temperature must be between 0.0 and 2.0!");
                    isValid = false;
                }

                if (config.geminiConfig.topP < 0f || config.geminiConfig.topP > 1f)
                {
                    Debug.LogError("Gemini topP must be between 0.0 and 1.0!");
                    isValid = false;
                }
            }

            // Check OpenAI configuration
            if (config.openAITextToSpeechConfig == null)
            {
                Debug.LogError("OpenAI Text-to-Speech configuration is missing!");
                isValid = false;
            }
            else
            {
                if (string.IsNullOrEmpty(config.openAITextToSpeechConfig.apiKey))
                {
                    Debug.LogWarning("OpenAI API key is empty!");
                }
            }

            return isValid;
        }

        /// <summary>
        /// Saves the configuration to PlayerPrefs as an example persistence method.
        /// </summary>
        /// <param name="config">The configuration to save.</param>
        private void SaveConfigurationToPlayerPrefs(GeneratorConfig config)
        {
            try
            {
                string configJson = JsonUtility.ToJson(config, true);
                PlayerPrefs.SetString("LastSubmittedConfig", configJson);
                PlayerPrefs.Save();
                Debug.Log("Configuration saved to PlayerPrefs successfully!");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Failed to save configuration to PlayerPrefs: {e.Message}");
            }
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Manually sets the config submitted event reference.
        /// </summary>
        /// <param name="eventAsset">The event asset to use.</param>
        public void SetConfigSubmittedEvent(ConfigSubmittedEvent eventAsset)
        {
            // Remove listener from old event
            if (configSubmittedEvent != null)
            {
                configSubmittedEvent.RemoveListener(OnConfigurationSubmitted);
            }

            // Set new event and add listener
            configSubmittedEvent = eventAsset;
            
            if (configSubmittedEvent != null && enabled)
            {
                configSubmittedEvent.AddListener(OnConfigurationSubmitted);
            }
        }

        #endregion
    }
}
