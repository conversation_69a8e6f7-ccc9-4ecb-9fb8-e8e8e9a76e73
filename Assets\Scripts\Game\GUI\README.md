# Runtime Configuration GUI System

This system provides a runtime GUI for configuring `GeneratorConfig` data using Unity's immediate mode GUI (OnGUI). The system includes event handling to trigger actions when the user submits configuration data.

## Components

### 1. RuntimeConfigGUI
The main GUI component that renders input fields for all `GeneratorConfig` properties:
- **Gemini Configuration**: API key, model, system instruction, temperature, topP, topK, thinking budget
- **OpenAI Text-to-Speech Configuration**: API key, API URL, model
- **General Configuration**: Asset addresses, scenario

### 2. ConfigSubmittedEvent
A ScriptableObject-based event system that is triggered when the user presses the submit button. This event carries the complete `GeneratorConfig` data object.

### 3. ConfigEventListener
An example component that demonstrates how to listen to the `ConfigSubmittedEvent` and process the submitted configuration data.

### 4. RuntimeGUIManager
A manager component that simplifies setup and provides easy control over the entire GUI system.

## Setup Instructions

### Quick Setup (Recommended)
1. Create an empty GameObject in your scene
2. Add the `RuntimeGUIManager` component to it
3. The manager will automatically set up all required components
4. Press F1 at runtime to toggle the GUI visibility

### Manual Setup
1. Create an empty GameObject in your scene
2. Add the `RuntimeConfigGUI` component
3. Add the `ConfigEventListener` component (or create your own listener)
4. Create a `ConfigSubmittedEvent` asset:
   - Right-click in Project window
   - Create > Game > Events > Config Submitted Event
5. Assign the event asset to both components

## Usage

### At Runtime
- Press **F1** to toggle GUI visibility (if using RuntimeGUIManager)
- Fill in the configuration fields
- Click **"Submit Configuration"** to trigger the event with the data
- Click **"Reset to Defaults"** to restore default values

### In Code
```csharp
// Get current configuration
var config = runtimeGUIManager.GetCurrentConfiguration();

// Load configuration into GUI
runtimeGUIManager.LoadConfiguration(someConfig);

// Listen to configuration events
configSubmittedEvent.AddListener(OnConfigurationReceived);

private void OnConfigurationReceived(GeneratorConfig config)
{
    // Process the submitted configuration
    Debug.Log($"Received config with scenario: {config.scenario}");
}
```

## Features

- **Scrollable Interface**: The GUI is scrollable to accommodate all fields
- **Input Validation**: Automatic parsing and validation of numeric fields
- **Event System**: ScriptableObject-based events for loose coupling
- **Keyboard Toggle**: F1 key to show/hide GUI (configurable)
- **Draggable Window**: GUI window can be moved around the screen
- **Default Values**: Reset button to restore default configuration values
- **Debug Support**: Built-in debugging methods and logging

## Customization

### Changing GUI Appearance
Modify the `InitializeStyles()` method in `RuntimeConfigGUI` to customize fonts, colors, and sizes.

### Adding New Fields
1. Add the field to the `GeneratorConfig` class
2. Add a corresponding string field in `RuntimeConfigGUI`
3. Add the field to `DrawConfigWindow()` method
4. Update `SetFieldsFromConfig()` and `CreateConfigFromFields()` methods

### Custom Event Handling
Create your own listener by inheriting from `MonoBehaviour` and subscribing to the `ConfigSubmittedEvent`:

```csharp
public class MyConfigProcessor : MonoBehaviour
{
    [SerializeField] private ConfigSubmittedEvent configEvent;
    
    private void OnEnable()
    {
        configEvent?.AddListener(ProcessConfig);
    }
    
    private void OnDisable()
    {
        configEvent?.RemoveListener(ProcessConfig);
    }
    
    private void ProcessConfig(GeneratorConfig config)
    {
        // Your custom processing logic here
    }
}
```

## Notes

- The GUI uses Unity's immediate mode GUI (OnGUI) for maximum compatibility
- All configuration data is validated before being passed to event listeners
- The system supports both runtime-created and asset-based event objects
- Input fields automatically handle type conversion (string to float/int)
- The GUI window is draggable and resizable within screen bounds
