using UnityEngine;
using Game.Events;

namespace Game.UI
{
    /// <summary>
    /// Manager component that handles the runtime GUI system and provides easy setup and control.
    /// </summary>
    public class RuntimeGUIManager : MonoBehaviour
    {
        #region Fields

        [Header("GUI Components")]
        [SerializeField] private RuntimeConfigGUI configGUI;
        [SerializeField] private ConfigEventListener eventListener;

        [Header("Event Assets")]
        [SerializeField] private ConfigSubmittedEvent configSubmittedEvent;

        [Header("Input Settings")]
        [SerializeField] private KeyCode toggleGUIKey = KeyCode.F1;
        [SerializeField] private bool enableKeyboardToggle = true;

        #endregion

        #region Properties

        /// <summary>
        /// Gets the runtime config GUI component.
        /// </summary>
        public RuntimeConfigGUI ConfigGUI => configGUI;

        /// <summary>
        /// Gets the config event listener component.
        /// </summary>
        public ConfigEventListener EventListener => eventListener;

        /// <summary>
        /// Gets or sets the config submitted event asset.
        /// </summary>
        public ConfigSubmittedEvent ConfigSubmittedEvent
        {
            get => configSubmittedEvent;
            set
            {
                configSubmittedEvent = value;
                UpdateEventReferences();
            }
        }

        #endregion

        #region Unity Lifecycle

        private void Awake()
        {
            InitializeComponents();
        }

        private void Start()
        {
            SetupEventSystem();
        }

        private void Update()
        {
            HandleInput();
        }

        #endregion

        #region Initialization

        /// <summary>
        /// Initializes the GUI components if they're not already assigned.
        /// </summary>
        private void InitializeComponents()
        {
            // Get or add RuntimeConfigGUI component
            if (configGUI == null)
            {
                configGUI = GetComponent<RuntimeConfigGUI>();
                if (configGUI == null)
                {
                    configGUI = gameObject.AddComponent<RuntimeConfigGUI>();
                }
            }

            // Get or add ConfigEventListener component
            if (eventListener == null)
            {
                eventListener = GetComponent<ConfigEventListener>();
                if (eventListener == null)
                {
                    eventListener = gameObject.AddComponent<ConfigEventListener>();
                }
            }
        }

        /// <summary>
        /// Sets up the event system connections.
        /// </summary>
        private void SetupEventSystem()
        {
            // Create event asset if not assigned
            if (configSubmittedEvent == null)
            {
                configSubmittedEvent = ScriptableObject.CreateInstance<ConfigSubmittedEvent>();
                Debug.Log("Created runtime ConfigSubmittedEvent instance.");
            }

            UpdateEventReferences();
        }

        /// <summary>
        /// Updates event references in all components.
        /// </summary>
        private void UpdateEventReferences()
        {
            if (configSubmittedEvent == null) return;

            // Update GUI component event reference
            if (configGUI != null)
            {
                configGUI.SetConfigSubmittedEvent(configSubmittedEvent);
            }

            // Update event listener reference
            if (eventListener != null)
            {
                eventListener.SetConfigSubmittedEvent(configSubmittedEvent);
            }
        }

        #endregion

        #region Input Handling

        /// <summary>
        /// Handles keyboard input for GUI control.
        /// </summary>
        private void HandleInput()
        {
            if (!enableKeyboardToggle) return;

            if (Input.GetKeyDown(toggleGUIKey))
            {
                ToggleGUI();
            }
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Toggles the visibility of the configuration GUI.
        /// </summary>
        public void ToggleGUI()
        {
            if (configGUI != null)
            {
                configGUI.ToggleGUI();
                Debug.Log($"GUI toggled. Now {(configGUI.ShowGUI ? "visible" : "hidden")}.");
            }
        }

        /// <summary>
        /// Shows the configuration GUI.
        /// </summary>
        public void ShowGUI()
        {
            if (configGUI != null)
            {
                configGUI.ShowGUI = true;
            }
        }

        /// <summary>
        /// Hides the configuration GUI.
        /// </summary>
        public void HideGUI()
        {
            if (configGUI != null)
            {
                configGUI.ShowGUI = false;
            }
        }

        /// <summary>
        /// Gets the current configuration from the GUI.
        /// </summary>
        /// <returns>The current configuration, or null if GUI is not available.</returns>
        public Data.GeneratorConfig GetCurrentConfiguration()
        {
            return configGUI?.GetCurrentConfiguration();
        }

        /// <summary>
        /// Loads a configuration into the GUI.
        /// </summary>
        /// <param name="config">The configuration to load.</param>
        public void LoadConfiguration(Data.GeneratorConfig config)
        {
            configGUI?.LoadConfiguration(config);
        }

        /// <summary>
        /// Creates a new ConfigSubmittedEvent asset and assigns it to the system.
        /// </summary>
        /// <returns>The created event asset.</returns>
        public ConfigSubmittedEvent CreateEventAsset()
        {
            var eventAsset = ScriptableObject.CreateInstance<ConfigSubmittedEvent>();
            ConfigSubmittedEvent = eventAsset;
            return eventAsset;
        }

        #endregion

        #region Debug Methods

        /// <summary>
        /// Logs the current state of the GUI system for debugging purposes.
        /// </summary>
        [ContextMenu("Debug GUI System State")]
        public void DebugGUISystemState()
        {
            Debug.Log("=== Runtime GUI System State ===");
            Debug.Log($"ConfigGUI: {(configGUI != null ? "Present" : "Missing")}");
            Debug.Log($"EventListener: {(eventListener != null ? "Present" : "Missing")}");
            Debug.Log($"ConfigSubmittedEvent: {(configSubmittedEvent != null ? "Present" : "Missing")}");
            Debug.Log($"GUI Visible: {(configGUI != null ? configGUI.ShowGUI.ToString() : "N/A")}");
            Debug.Log($"Toggle Key: {toggleGUIKey}");
            Debug.Log($"Keyboard Toggle Enabled: {enableKeyboardToggle}");
            Debug.Log("=== End GUI System State ===");
        }

        #endregion
    }
}
