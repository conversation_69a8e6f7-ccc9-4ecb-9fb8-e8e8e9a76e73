using UnityEngine;
using UnityEngine.Events;
using Game.Data;

namespace Game.UI
{
    /// <summary>
    /// Manager component that handles the runtime GUI system and provides easy setup and control.
    /// </summary>
    public class RuntimeGUIManager : MonoBehaviour
    {
        #region Fields

        [Header("GUI Components")]
        [SerializeField] private RuntimeConfigGUI configGUI;

        [Header("Events")]
        [SerializeField] private UnityEvent<GeneratorConfig> onConfigSubmitted;

        [<PERSON><PERSON>("Input Settings")]
        [SerializeField] private KeyCode toggleGUIKey = KeyCode.F1;
        [SerializeField] private bool enableKeyboardToggle = true;

        #endregion

        #region Properties

        /// <summary>
        /// Gets the runtime config GUI component.
        /// </summary>
        public RuntimeConfigGUI ConfigGUI => configGUI;

        /// <summary>
        /// Gets or sets the config submitted event.
        /// </summary>
        public UnityEvent<GeneratorConfig> OnConfigSubmitted
        {
            get => onConfigSubmitted;
            set => onConfigSubmitted = value;
        }

        #endregion

        #region Unity Lifecycle

        private void Awake()
        {
            InitializeComponents();
        }

        private void Start()
        {
            SetupEventSystem();
        }

        private void Update()
        {
            HandleInput();
        }

        #endregion

        #region Initialization

        /// <summary>
        /// Initializes the GUI components if they're not already assigned.
        /// </summary>
        private void InitializeComponents()
        {
            // Get or add RuntimeConfigGUI component
            if (configGUI == null)
            {
                configGUI = GetComponent<RuntimeConfigGUI>();
                if (configGUI == null)
                {
                    configGUI = gameObject.AddComponent<RuntimeConfigGUI>();
                }
            }
        }

        /// <summary>
        /// Sets up the event system connections.
        /// </summary>
        private void SetupEventSystem()
        {
            // Initialize event if not assigned
            if (onConfigSubmitted == null)
            {
                onConfigSubmitted = new UnityEvent<GeneratorConfig>();
            }

            // Connect the GUI's event to our manager's event
            if (configGUI != null && configGUI.onConfigSubmitted != null)
            {
                configGUI.onConfigSubmitted.AddListener(OnConfigurationSubmitted);
            }
        }

        /// <summary>
        /// Handles configuration submission from the GUI.
        /// </summary>
        private void OnConfigurationSubmitted(GeneratorConfig config)
        {
            // Forward the event to any listeners
            onConfigSubmitted?.Invoke(config);
        }

        #endregion

        #region Input Handling

        /// <summary>
        /// Handles keyboard input for GUI control.
        /// </summary>
        private void HandleInput()
        {
            if (!enableKeyboardToggle) return;

            if (Input.GetKeyDown(toggleGUIKey))
            {
                ToggleGUI();
            }
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Toggles the visibility of the configuration GUI.
        /// </summary>
        public void ToggleGUI()
        {
            if (configGUI != null)
            {
                configGUI.ToggleGUI();
                Debug.Log($"GUI toggled. Now {(configGUI.ShowGUI ? "visible" : "hidden")}.");
            }
        }

        /// <summary>
        /// Shows the configuration GUI.
        /// </summary>
        public void ShowGUI()
        {
            if (configGUI != null)
            {
                configGUI.ShowGUI = true;
            }
        }

        /// <summary>
        /// Hides the configuration GUI.
        /// </summary>
        public void HideGUI()
        {
            if (configGUI != null)
            {
                configGUI.ShowGUI = false;
            }
        }

        /// <summary>
        /// Gets the current configuration from the GUI.
        /// </summary>
        /// <returns>The current configuration, or null if GUI is not available.</returns>
        public GeneratorConfig GetCurrentConfiguration()
        {
            return configGUI?.GetCurrentConfiguration();
        }

        /// <summary>
        /// Loads a configuration into the GUI.
        /// </summary>
        /// <param name="config">The configuration to load.</param>
        public void LoadConfiguration(GeneratorConfig config)
        {
            configGUI?.LoadConfiguration(config);
        }

        #endregion

        #region Debug Methods

        /// <summary>
        /// Logs the current state of the GUI system for debugging purposes.
        /// </summary>
        [ContextMenu("Debug GUI System State")]
        public void DebugGUISystemState()
        {
            Debug.Log("=== Runtime GUI System State ===");
            Debug.Log($"ConfigGUI: {(configGUI != null ? "Present" : "Missing")}");
            Debug.Log($"OnConfigSubmitted Event: {(onConfigSubmitted != null ? "Present" : "Missing")}");
            Debug.Log($"GUI Visible: {(configGUI != null ? configGUI.ShowGUI.ToString() : "N/A")}");
            Debug.Log($"Toggle Key: {toggleGUIKey}");
            Debug.Log($"Keyboard Toggle Enabled: {enableKeyboardToggle}");
            Debug.Log("=== End GUI System State ===");
        }

        #endregion
    }
}
