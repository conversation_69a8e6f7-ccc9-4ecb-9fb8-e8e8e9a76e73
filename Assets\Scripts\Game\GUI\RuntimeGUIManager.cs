using UnityEngine;

namespace Game.UI
{
    /// <summary>
    /// Manager component that handles the runtime GUI system and provides easy setup and control.
    /// </summary>
    public class RuntimeGUIManager : MonoBehaviour
    {
        #region Fields

        [Header("GUI Components")]
        [SerializeField] private RuntimeConfigGUI configGUI;



        [Header("Input Settings")]
        [SerializeField] private KeyCode toggleGUIKey = KeyCode.F1;
        [SerializeField] private bool enableKeyboardToggle = true;

        #endregion

        #region Properties

        /// <summary>
        /// Gets the runtime config GUI component.
        /// </summary>
        public RuntimeConfigGUI ConfigGUI => configGUI;



        #endregion

        #region Unity Lifecycle

        private void Awake()
        {
            InitializeComponents();
        }



        private void Update()
        {
            HandleInput();
        }

        #endregion

        #region Initialization

        /// <summary>
        /// Initializes the GUI components if they're not already assigned.
        /// </summary>
        private void InitializeComponents()
        {
            // Get or add RuntimeConfigGUI component
            if (configGUI == null)
            {
                configGUI = GetComponent<RuntimeConfigGUI>();
                if (configGUI == null)
                {
                    configGUI = gameObject.AddComponent<RuntimeConfigGUI>();
                }
            }
        }

        #endregion

        #region Input Handling

        /// <summary>
        /// Handles keyboard input for GUI control.
        /// </summary>
        private void HandleInput()
        {
            if (!enableKeyboardToggle) return;

            if (Input.GetKeyDown(toggleGUIKey))
            {
                ToggleGUI();
            }
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Toggles the visibility of the configuration GUI.
        /// </summary>
        public void ToggleGUI()
        {
            if (configGUI != null)
            {
                configGUI.ToggleGUI();
                Debug.Log($"GUI toggled. Now {(configGUI.ShowGUI ? "visible" : "hidden")}.");
            }
        }

        #endregion

        #region Debug Methods

        /// <summary>
        /// Logs the current state of the GUI system for debugging purposes.
        /// </summary>
        [ContextMenu("Debug GUI System State")]
        public void DebugGUISystemState()
        {
            Debug.Log("=== Runtime GUI System State ===");
            Debug.Log($"ConfigGUI: {(configGUI != null ? "Present" : "Missing")}");
            Debug.Log($"GUI Event: {(configGUI != null && configGUI.onConfigSubmitted != null ? "Present" : "Missing")}");
            Debug.Log($"GUI Visible: {(configGUI != null ? configGUI.ShowGUI.ToString() : "N/A")}");
            Debug.Log($"Toggle Key: {toggleGUIKey}");
            Debug.Log($"Keyboard Toggle Enabled: {enableKeyboardToggle}");
            Debug.Log("=== End GUI System State ===");
        }

        #endregion
    }
}
