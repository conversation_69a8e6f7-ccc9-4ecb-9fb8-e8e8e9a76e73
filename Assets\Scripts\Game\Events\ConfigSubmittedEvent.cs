using UnityEngine;
using UnityEngine.Events;
using Game.Data;

namespace Game.Events
{
    /// <summary>
    /// ScriptableObject event that is raised when a GeneratorConfig is submitted.
    /// </summary>
    [CreateAssetMenu(fileName = "ConfigSubmittedEvent", menuName = "Game/Events/Config Submitted Event")]
    public class ConfigSubmittedEvent : ScriptableObject
    {
        #region Fields

        /// <summary>
        /// Unity event that is invoked when the configuration is submitted.
        /// </summary>
        [SerializeField] private UnityEvent<GeneratorConfig> onConfigSubmitted = new UnityEvent<GeneratorConfig>();

        #endregion

        #region Events

        /// <summary>
        /// Event that is raised when a configuration is submitted.
        /// </summary>
        public UnityEvent<GeneratorConfig> OnConfigSubmitted => onConfigSubmitted;

        #endregion

        #region Public Methods

        /// <summary>
        /// Raises the config submitted event with the provided configuration.
        /// </summary>
        /// <param name="config">The configuration that was submitted.</param>
        public void Raise(GeneratorConfig config)
        {
            if (config == null)
            {
                Debug.LogWarning("Attempted to raise ConfigSubmittedEvent with null configuration.");
                return;
            }

            Debug.Log($"ConfigSubmittedEvent raised with scenario: {config.scenario}");
            onConfigSubmitted?.Invoke(config);
        }

        /// <summary>
        /// Adds a listener to the config submitted event.
        /// </summary>
        /// <param name="listener">The listener to add.</param>
        public void AddListener(UnityAction<GeneratorConfig> listener)
        {
            onConfigSubmitted.AddListener(listener);
        }

        /// <summary>
        /// Removes a listener from the config submitted event.
        /// </summary>
        /// <param name="listener">The listener to remove.</param>
        public void RemoveListener(UnityAction<GeneratorConfig> listener)
        {
            onConfigSubmitted.RemoveListener(listener);
        }

        /// <summary>
        /// Removes all listeners from the config submitted event.
        /// </summary>
        public void RemoveAllListeners()
        {
            onConfigSubmitted.RemoveAllListeners();
        }

        #endregion
    }
}
